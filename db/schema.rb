# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_27_190902) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "api_tokens", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "name", null: false
    t.string "token", null: false
    t.datetime "last_used_at"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "scopes", default: [], array: true
    t.index ["token"], name: "index_api_tokens_on_token", unique: true
    t.index ["user_id"], name: "index_api_tokens_on_user_id"
  end

  create_table "blog_posts", force: :cascade do |t|
    t.string "title", null: false
    t.string "slug", null: false
    t.string "category"
    t.string "author"
    t.string "author_title"
    t.string "author_avatar"
    t.datetime "published_at"
    t.string "reading_time"
    t.string "media_type"
    t.string "media_url"
    t.string "video_duration"
    t.integer "episode_number"
    t.text "content"
    t.boolean "featured", default: false
    t.text "tags", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_blog_posts_on_category"
    t.index ["featured"], name: "index_blog_posts_on_featured"
    t.index ["published_at"], name: "index_blog_posts_on_published_at"
    t.index ["slug"], name: "index_blog_posts_on_slug", unique: true
  end

  create_table "custom_domains", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "domain"
    t.boolean "verified"
    t.datetime "verified_at"
    t.string "verification_token"
    t.text "certificate_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_primary", default: false
    t.bigint "team_id"
    t.index ["domain"], name: "index_custom_domains_on_domain", unique: true
    t.index ["team_id"], name: "index_custom_domains_on_team_id"
    t.index ["user_id", "is_primary"], name: "index_custom_domains_on_user_id_and_is_primary", unique: true, where: "(is_primary = true)"
    t.index ["user_id"], name: "index_custom_domains_on_user_id"
  end

  create_table "link_clicks", force: :cascade do |t|
    t.bigint "link_id", null: false
    t.jsonb "attribution_data", default: {}, null: false
    t.jsonb "tracking_data", default: {}, null: false
    t.datetime "clicked_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index "((attribution_data ->> 'referrer'::text))", name: "index_link_clicks_on_attribution_data_referrer"
    t.index "((attribution_data ->> 'utm_source'::text))", name: "index_link_clicks_on_attribution_data_utm_source"
    t.index "((tracking_data ->> 'country_code'::text))", name: "index_link_clicks_on_tracking_data_country_code"
    t.index "((tracking_data ->> 'ip_address'::text))", name: "index_link_clicks_on_tracking_data_ip_address"
    t.index ["attribution_data"], name: "index_link_clicks_on_attribution_data", using: :gin
    t.index ["clicked_at"], name: "index_link_clicks_on_clicked_at"
    t.index ["link_id", "clicked_at"], name: "index_link_clicks_on_link_id_and_clicked_at"
    t.index ["link_id", "created_at"], name: "index_link_clicks_on_link_id_and_created_at"
    t.index ["link_id"], name: "index_link_clicks_on_link_id"
    t.index ["tracking_data"], name: "index_link_clicks_on_tracking_data", using: :gin
  end

  create_table "links", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "team_id"
    t.string "original_url", limit: 2048, null: false
    t.string "short_code", limit: 50, null: false
    t.jsonb "metadata", default: {}
    t.integer "link_clicks_count", default: 0, null: false
    t.datetime "archived_at"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_archived", default: false, null: false
    t.bigint "custom_domain_id"
    t.index ["archived_at"], name: "index_links_on_archived_at", where: "(archived_at IS NOT NULL)"
    t.index ["custom_domain_id"], name: "index_links_on_custom_domain_id"
    t.index ["expires_at"], name: "index_links_on_expires_at", where: "(expires_at IS NOT NULL)"
    t.index ["metadata"], name: "index_links_on_metadata", using: :gin
    t.index ["short_code"], name: "index_links_on_short_code", unique: true
    t.index ["team_id", "created_at"], name: "index_links_on_team_id_and_created_at", where: "(team_id IS NOT NULL)"
    t.index ["team_id"], name: "index_links_on_team_id"
    t.index ["user_id", "created_at"], name: "index_links_on_user_id_and_created_at"
    t.index ["user_id", "is_archived"], name: "index_links_on_user_id_and_is_archived"
    t.index ["user_id"], name: "index_links_on_user_id"
  end

  create_table "solid_cable_messages", force: :cascade do |t|
    t.text "channel"
    t.text "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_solid_cable_messages_on_created_at"
  end

  create_table "solid_cache_entries", force: :cascade do |t|
    t.binary "key", null: false
    t.binary "value", null: false
    t.datetime "created_at", null: false
    t.bigint "key_hash", null: false
    t.integer "byte_size", null: false
    t.index ["byte_size"], name: "index_solid_cache_entries_on_byte_size"
    t.index ["created_at"], name: "index_solid_cache_entries_on_created_at"
    t.index ["key"], name: "index_solid_cache_entries_on_key", unique: true
    t.index ["key_hash"], name: "index_solid_cache_entries_on_key_hash", unique: true
  end

  create_table "solid_queue_blocked_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.string "concurrency_key", null: false
    t.datetime "expires_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at", "concurrency_key"], name: "index_solid_queue_blocked_executions_for_maintenance"
    t.index ["job_id"], name: "index_solid_queue_blocked_executions_on_job_id", unique: true
  end

  create_table "solid_queue_claimed_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.bigint "process_id"
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_claimed_executions_on_job_id", unique: true
    t.index ["process_id", "id"], name: "index_solid_queue_claimed_executions_on_process_id_and_id"
  end

  create_table "solid_queue_failed_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.text "error"
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_failed_executions_on_job_id", unique: true
  end

  create_table "solid_queue_jobs", force: :cascade do |t|
    t.string "queue_name", null: false
    t.string "class_name", null: false
    t.text "arguments"
    t.integer "priority", default: 0, null: false
    t.string "active_job_id"
    t.datetime "scheduled_at"
    t.datetime "finished_at"
    t.string "concurrency_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active_job_id"], name: "index_solid_queue_jobs_on_active_job_id"
    t.index ["class_name"], name: "index_solid_queue_jobs_on_class_name"
    t.index ["finished_at"], name: "index_solid_queue_jobs_on_finished_at"
    t.index ["queue_name", "finished_at"], name: "index_solid_queue_jobs_for_filtering"
    t.index ["scheduled_at", "finished_at"], name: "index_solid_queue_jobs_for_alerting"
  end

  create_table "solid_queue_pauses", force: :cascade do |t|
    t.string "queue_name", null: false
    t.datetime "created_at", null: false
    t.index ["queue_name"], name: "index_solid_queue_pauses_on_queue_name", unique: true
  end

  create_table "solid_queue_processes", force: :cascade do |t|
    t.string "kind", null: false
    t.datetime "last_heartbeat_at", null: false
    t.bigint "supervisor_id"
    t.integer "pid", null: false
    t.string "hostname"
    t.text "metadata"
    t.datetime "created_at", null: false
    t.index ["kind"], name: "index_solid_queue_processes_on_kind"
    t.index ["last_heartbeat_at"], name: "index_solid_queue_processes_on_last_heartbeat_at"
    t.index ["supervisor_id"], name: "index_solid_queue_processes_on_supervisor_id"
  end

  create_table "solid_queue_ready_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["job_id"], name: "index_solid_queue_ready_executions_on_job_id", unique: true
    t.index ["priority", "job_id"], name: "index_solid_queue_poll_all"
  end

  create_table "solid_queue_scheduled_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.datetime "scheduled_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["job_id"], name: "index_solid_queue_scheduled_executions_on_job_id", unique: true
    t.index ["scheduled_at", "priority"], name: "index_solid_queue_dispatch_all"
  end

  create_table "solid_queue_semaphores", force: :cascade do |t|
    t.string "key", null: false
    t.integer "value", default: 1, null: false
    t.datetime "expires_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_solid_queue_semaphores_on_expires_at"
    t.index ["key", "value"], name: "index_solid_queue_semaphores_on_key_and_value"
    t.index ["key"], name: "index_solid_queue_semaphores_on_key", unique: true
  end

  create_table "team_memberships", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "team_id", null: false
    t.string "role", default: "member"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["team_id"], name: "index_team_memberships_on_team_id"
    t.index ["user_id", "team_id"], name: "index_team_memberships_on_user_id_and_team_id", unique: true
    t.index ["user_id"], name: "index_team_memberships_on_user_id"
  end

  create_table "teams", force: :cascade do |t|
    t.string "name", null: false
    t.string "slug", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "default_domain"
    t.text "description"
    t.index ["slug"], name: "index_teams_on_slug", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "timezone"
    t.string "subscription_plan"
    t.boolean "email_notifications", default: true, null: false
    t.boolean "two_factor_enabled", default: false, null: false
    t.string "company"
    t.string "job_title"
    t.boolean "weekly_reports"
    t.boolean "marketing_emails"
    t.boolean "real_time_alerts"
    t.integer "session_timeout"
    t.boolean "google_analytics_enabled"
    t.boolean "slack_enabled"
    t.boolean "zapier_enabled"
    t.boolean "webhook_enabled"
    t.boolean "facebook_pixel_enabled"
    t.string "google_analytics_tracking_id"
    t.string "facebook_pixel_id"
    t.string "slack_webhook_url"
    t.string "zapier_webhook_url"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "api_tokens", "users"
  add_foreign_key "custom_domains", "teams"
  add_foreign_key "custom_domains", "users"
  add_foreign_key "link_clicks", "links"
  add_foreign_key "links", "custom_domains"
  add_foreign_key "links", "teams"
  add_foreign_key "links", "users"
  add_foreign_key "team_memberships", "teams"
  add_foreign_key "team_memberships", "users"
end
