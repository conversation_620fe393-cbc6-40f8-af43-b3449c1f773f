<% 
  nav_items = [
    { name: 'Dashboard', path: authenticated_root_path, icon: 'chart-bar', color: 'purple' },
    { name: 'Links', path: links_path, icon: 'link', color: 'gray' },
    { name: 'Analytics', path: analytics_path, icon: 'chart-line', color: 'gray' },
  ]
  
  current_path = request.path
%>

<% nav_items.each do |item| %>
  <% 
    is_active = case item[:name]
    when 'Dashboard'
      current_path == authenticated_root_path || current_path == '/dashboard'
    when 'Links'
      current_path.start_with?('/links')
    when 'Analytics'
      current_path.start_with?('/analytics')
    else
      false
    end
  %>
  <%= link_to item[:path], 
      class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors #{is_active ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'}",
      title: item[:name] do %>
    <svg class="w-5 h-5 flex-shrink-0 <%= is_active ? 'text-purple-600' : 'text-gray-400' %> md:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <% case item[:icon] %>
      <% when 'chart-bar' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
      <% when 'link' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
      <% when 'chart-line' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
      <% end %>
    </svg>
    <span class="hidden md:block"><%= item[:name] %></span>
  <% end %>
<% end %>

<!-- Divider -->
<hr class="my-4 border-gray-200">

<!-- Admin Panel Section -->
<div class="mb-2">
  <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Admin Panel</h3>
</div>

<% 
  admin_items = [
    { name: 'Settings', path: settings_path, icon: 'cog', color: 'gray' },
    { name: 'API Tokens', path: settings_path(section: 'api_tokens'), icon: 'key', color: 'gray' },
    { name: 'Custom Domains', path: settings_path(section: 'domains'), icon: 'globe', color: 'gray' },
    { name: 'Team', path: settings_path(section: 'team'), icon: 'users', color: 'gray' },
  ]
%>

<% admin_items.each do |item| %>
  <% 
    is_active = case item[:name]
    when 'Settings'
      current_path == '/settings' && (params[:section].nil? || params[:section] == 'account')
    when 'API Tokens'
      current_path == '/settings' && params[:section] == 'api_tokens'
    when 'Custom Domains'
      current_path == '/settings' && params[:section] == 'domains'
    when 'Team'
      current_path == '/settings' && params[:section] == 'team'
    else
      false
    end
  %>
  <%= link_to item[:path], 
      class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors #{is_active ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'}",
      title: item[:name] do %>
    <svg class="w-5 h-5 flex-shrink-0 <%= is_active ? 'text-purple-600' : 'text-gray-400' %> md:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <% case item[:icon] %>
      <% when 'cog' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      <% when 'key' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
      <% when 'globe' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
      <% when 'users' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
      <% end %>
    </svg>
    <span class="hidden md:block"><%= item[:name] %></span>
  <% end %>
<% end %>

<!-- Divider -->
<hr class="my-4 border-gray-200">

<!-- Secondary Navigation -->
<% 
  secondary_items = [
    { name: 'QR Codes', path: '#', icon: 'qr-code' },
    { name: 'Bulk Links', path: '#', icon: 'upload' },
    { name: 'Billing', path: settings_path(section: 'billing'), icon: 'credit-card' },
    { name: 'Integrations', path: settings_path(section: 'integrations'), icon: 'puzzle' },
    { name: 'Help', path: '/help', icon: 'help' },
  ]
%>

<% secondary_items.each do |item| %>
  <%= link_to item[:path], 
      class: "group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-50 transition-colors",
      title: item[:name] do %>
    <svg class="w-5 h-5 flex-shrink-0 text-gray-400 md:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <% case item[:icon] %>
      <% when 'qr-code' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h2M4 12h4m12 0h.01M4 20h2m2-2v-6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v6M7 8V6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2"></path>
      <% when 'upload' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
      <% when 'credit-card' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
      <% when 'puzzle' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
      <% when 'help' %>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      <% end %>
    </svg>
    <span class="hidden md:block"><%= item[:name] %></span>
  <% end %>
<% end %>