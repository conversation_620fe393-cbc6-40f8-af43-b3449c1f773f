module ChartHelper
  # Default color palette for charts
  CHART_COLORS = {
    primary: '99, 102, 241',      # Indigo
    success: '34, 197, 94',       # Green  
    warning: '251, 146, 60',      # Orange
    danger: '239, 68, 68',        # Red
    info: '14, 165, 233',         # Sky Blue
    purple: '168, 85, 247',       # Purple
    pink: '236, 72, 153',         # Pink
    amber: '245, 158, 11',        # <PERSON>
    gray: '107, 114, 128'         # Gray
  }.freeze

  # Color schemes for different chart types
  CHART_COLOR_SCHEMES = {
    single: [:primary],
    dual: [:primary, :success],
    multi: [:primary, :success, :warning, :danger, :info],
    rainbow: [:primary, :success, :warning, :danger, :purple, :pink, :amber, :info]
  }.freeze

  def chart_colors(scheme = :multi)
    colors = CHART_COLOR_SCHEMES[scheme] || CHART_COLOR_SCHEMES[:multi]
    colors.map { |color| CHART_COLORS[color] }
  end

  def chart_container(type:, data:, label: nil, colors: nil, height: '300px', options: {}, &block)
    colors ||= chart_colors(data.size <= 2 ? :dual : :multi)
    
    # Convert array data to object format expected by chart controller
    chart_data = case data
    when Array
      # Handle array of arrays like [["US", 38], ["JP", 36]]
      if data.first.is_a?(Array)
        data.to_h
      else
        # Handle simple array [1, 2, 3] -> {"0" => 1, "1" => 2, "2" => 3}
        data.each_with_index.to_h { |value, index| [index.to_s, value] }
      end
    when Hash
      data
    else
      {}
    end
    
    content_tag :div, 
      class: 'chart-container',
      data: {
        controller: 'chart',
        'chart-type-value': type,
        'chart-data-value': chart_data.to_json,
        'chart-label-value': label,
        'chart-colors-value': colors.to_json,
        'chart-options-value': options.to_json
      },
      style: "height: #{height}" do
      content_tag(:canvas) + (block_given? ? capture(&block) : '')
    end
  end

  def line_chart(data, label: 'Data', height: '300px', **options)
    chart_container(type: 'line', data: data, label: label, colors: [CHART_COLORS[:primary]], height: height, options: options)
  end

  def bar_chart(data, label: 'Data', height: '300px', **options)
    chart_container(type: 'bar', data: data, label: label, colors: [CHART_COLORS[:success]], height: height, options: options)
  end

  def pie_chart(data, height: '300px', **options)
    chart_container(type: 'pie', data: data, height: height, options: options)
  end

  def doughnut_chart(data, height: '300px', **options)
    chart_container(type: 'doughnut', data: data, height: height, options: options)
  end
end