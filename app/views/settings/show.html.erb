<% content_for :page_title, "Settings" %>
<% content_for :header_actions do %>
  <%= link_to "Export Settings", "#", class: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium flex items-center space-x-2" %>
<% end %>

<div class="p-6" data-controller="settings">
  <!-- Header Section -->
  <div class="mb-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-1">Settings</h1>
    <p class="text-gray-600">Manage your account preferences and configurations</p>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
    <!-- Sidebar Navigation -->
    <div class="lg:col-span-1">
      <nav class="space-y-1" data-settings-target="navigation">
        <!-- Account -->
        <%= link_to settings_path(section: 'account'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-purple-50 text-purple-700 border border-purple-200' if @current_section == 'account'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'account'}" do %>
          <div class="w-10 h-10 <%= @current_section == 'account' ? 'bg-gradient-to-br from-purple-500 to-purple-600' : 'bg-gradient-to-br from-purple-500 to-purple-600' %> rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'account' ? 'text-purple-700' : 'text-gray-900 group-hover:text-gray-900' %>">Account</span>
            <p class="text-xs <%= @current_section == 'account' ? 'text-purple-600' : 'text-gray-500' %>">Personal information</p>
          </div>
        <% end %>

        <!-- Team -->
        <%= link_to settings_path(section: 'team'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-blue-50 text-blue-700 border border-blue-200' if @current_section == 'team'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'team'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'team' ? 'text-blue-700' : 'text-gray-900 group-hover:text-gray-900' %>">Team</span>
            <p class="text-xs <%= @current_section == 'team' ? 'text-blue-600' : 'text-gray-500' %>">Collaboration settings</p>
          </div>
        <% end %>

        <!-- API Tokens -->
        <%= link_to settings_path(section: 'api_tokens'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-orange-50 text-orange-700 border border-orange-200' if @current_section == 'api_tokens'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'api_tokens'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'api_tokens' ? 'text-orange-700' : 'text-gray-900 group-hover:text-gray-900' %>">API Tokens</span>
            <p class="text-xs <%= @current_section == 'api_tokens' ? 'text-orange-600' : 'text-gray-500' %>">API access keys</p>
          </div>
        <% end %>

        <!-- Billing -->
        <%= link_to settings_path(section: 'billing'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-yellow-50 text-yellow-700 border border-yellow-200' if @current_section == 'billing'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'billing'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'billing' ? 'text-yellow-700' : 'text-gray-900 group-hover:text-gray-900' %>">Billing</span>
            <p class="text-xs <%= @current_section == 'billing' ? 'text-yellow-600' : 'text-gray-500' %>">Plans & payments</p>
          </div>
        <% end %>

        <!-- Notifications -->
        <%= link_to settings_path(section: 'notifications'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-violet-50 text-violet-700 border border-violet-200' if @current_section == 'notifications'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'notifications'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-violet-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'notifications' ? 'text-violet-700' : 'text-gray-900 group-hover:text-gray-900' %>">Notifications</span>
            <p class="text-xs <%= @current_section == 'notifications' ? 'text-violet-600' : 'text-gray-500' %>">Alerts & emails</p>
          </div>
        <% end %>

        <!-- Security -->
        <%= link_to settings_path(section: 'security'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-red-50 text-red-700 border border-red-200' if @current_section == 'security'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'security'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'security' ? 'text-red-700' : 'text-gray-900 group-hover:text-gray-900' %>">Security</span>
            <p class="text-xs <%= @current_section == 'security' ? 'text-red-600' : 'text-gray-500' %>">Password & 2FA</p>
          </div>
        <% end %>

        <!-- Domains -->
        <%= link_to settings_path(section: 'domains'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-teal-50 text-teal-700 border border-teal-200' if @current_section == 'domains'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'domains'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'domains' ? 'text-teal-700' : 'text-gray-900 group-hover:text-gray-900' %>">Domains</span>
            <p class="text-xs <%= @current_section == 'domains' ? 'text-teal-600' : 'text-gray-500' %>">Custom domains</p>
          </div>
        <% end %>

        <!-- Integrations -->
        <%= link_to settings_path(section: 'integrations'), 
                    class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-pink-50 text-pink-700 border border-pink-200' if @current_section == 'integrations'} #{'hover:bg-gray-50 hover:shadow-sm' unless @current_section == 'integrations'}" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <span class="font-medium <%= @current_section == 'integrations' ? 'text-pink-700' : 'text-gray-900 group-hover:text-gray-900' %>">Integrations</span>
            <p class="text-xs <%= @current_section == 'integrations' ? 'text-pink-600' : 'text-gray-500' %>">Third-party apps</p>
          </div>
        <% end %>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="lg:col-span-3">
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 min-h-[600px] hover:shadow-xl transition-shadow duration-300">
        <div class="p-8">
          <% case @current_section %>
          <% when 'account' %>
            <%= render 'settings/sections/account', user: @user %>
          <% when 'team' %>
            <%= render 'settings/sections/team', user: @user %>
          <% when 'api_tokens' %>
            <%= render 'settings/sections/api_tokens', user: @user %>
          <% when 'billing' %>
            <%= render 'settings/sections/billing', user: @user %>
          <% when 'notifications' %>
            <%= render 'settings/sections/notifications', user: @user %>
          <% when 'security' %>
            <%= render 'settings/sections/security', user: @user %>
          <% when 'domains' %>
            <%= render 'settings/sections/domains', user: @user %>
          <% when 'integrations' %>
            <%= render 'settings/sections/integrations', user: @user %>
          <% else %>
            <%= render 'settings/sections/account', user: @user %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>