<%# Shared dropdown styling partial for consistent appearance across all dropdowns %>
<%# Usage: <%= render 'shared/dropdown_styles', dropdown_id: 'unique-id', trigger_classes: 'custom-trigger-classes', menu_classes: 'custom-menu-classes' %>

<%
  # Default classes for dropdown components
  default_trigger_classes = "relative inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
  
  default_menu_classes = "absolute right-0 z-50 mt-2 w-64 origin-top-right bg-white border border-gray-200 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden animate-in fade-in-0 zoom-in-95 duration-200"
  
  # Merge with custom classes if provided
  trigger_classes = local_assigns[:trigger_classes] ? "#{default_trigger_classes} #{local_assigns[:trigger_classes]}" : default_trigger_classes
  menu_classes = local_assigns[:menu_classes] ? "#{default_menu_classes} #{local_assigns[:menu_classes]}" : default_menu_classes
  
  # Generate unique IDs if not provided
  dropdown_id = local_assigns[:dropdown_id] || "dropdown-#{SecureRandom.hex(4)}"
  trigger_id = "#{dropdown_id}-trigger"
  menu_id = "#{dropdown_id}-menu"
%>

<style>
  /* Enhanced dropdown animations */
  .animate-in {
    animation-duration: 200ms;
    animation-fill-mode: both;
  }
  
  .fade-in-0 {
    animation-name: fadeIn;
  }
  
  .zoom-in-95 {
    animation-name: zoomIn;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes zoomIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  /* Dropdown menu item styles */
  .dropdown-item {
    @apply flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150 cursor-pointer;
  }
  
  .dropdown-item:focus {
    @apply bg-gray-50 text-gray-900 outline-none;
  }
  
  .dropdown-item.active {
    @apply bg-blue-50 text-blue-700;
  }
  
  .dropdown-item.disabled {
    @apply text-gray-400 cursor-not-allowed hover:bg-transparent hover:text-gray-400;
  }
  
  /* Dropdown divider */
  .dropdown-divider {
    @apply border-t border-gray-200 my-1;
  }
  
  /* Dropdown header */
  .dropdown-header {
    @apply px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50;
  }
  
  /* Icon styles within dropdowns */
  .dropdown-icon {
    @apply w-5 h-5 mr-3 text-gray-400;
  }
  
  .dropdown-item:hover .dropdown-icon {
    @apply text-gray-500;
  }
  
  /* Badge styles within dropdowns */
  .dropdown-badge {
    @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
  }
  
  .dropdown-badge.primary {
    @apply bg-blue-100 text-blue-800;
  }
  
  .dropdown-badge.success {
    @apply bg-green-100 text-green-800;
  }
  
  .dropdown-badge.warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .dropdown-badge.danger {
    @apply bg-red-100 text-red-800;
  }
  
  /* Notification specific styles */
  .notification-item {
    @apply dropdown-item border-l-4 border-transparent;
  }
  
  .notification-item.unread {
    @apply border-blue-400 bg-blue-50;
  }
  
  .notification-item .notification-content {
    @apply flex-1 min-w-0;
  }
  
  .notification-item .notification-title {
    @apply font-medium text-gray-900 truncate;
  }
  
  .notification-item .notification-description {
    @apply text-sm text-gray-500 truncate;
  }
  
  .notification-item .notification-time {
    @apply text-xs text-gray-400 mt-1;
  }
  
  /* User dropdown specific styles */
  .user-info {
    @apply flex items-center px-4 py-3 border-b border-gray-200;
  }
  
  .user-avatar {
    @apply w-10 h-10 rounded-full mr-3;
  }
  
  .user-details {
    @apply flex-1 min-w-0;
  }
  
  .user-name {
    @apply font-medium text-gray-900 truncate;
  }
  
  .user-email {
    @apply text-sm text-gray-500 truncate;
  }
  
  .user-plan {
    @apply text-xs text-blue-600 font-medium;
  }
  
  /* Resources dropdown specific styles */
  .resource-item {
    @apply dropdown-item;
  }
  
  .resource-item .resource-icon {
    @apply w-8 h-8 mr-3 p-1.5 bg-gray-100 rounded-lg;
  }
  
  .resource-item .resource-content {
    @apply flex-1;
  }
  
  .resource-item .resource-title {
    @apply font-medium text-gray-900;
  }
  
  .resource-item .resource-description {
    @apply text-sm text-gray-500 mt-0.5;
  }
  
  /* Mobile responsive adjustments */
  @media (max-width: 640px) {
    .dropdown-menu {
      @apply w-screen max-w-sm;
    }
    
    .dropdown-item {
      @apply px-6 py-4;
    }
  }
  
  /* Dark mode support (if needed) */
  @media (prefers-color-scheme: dark) {
    .dropdown-menu {
      @apply bg-gray-800 border-gray-700;
    }
    
    .dropdown-item {
      @apply text-gray-200 hover:bg-gray-700 hover:text-white;
    }
    
    .dropdown-divider {
      @apply border-gray-700;
    }
    
    .dropdown-header {
      @apply text-gray-400 bg-gray-700;
    }
  }
</style>

<%# Export the generated classes and IDs for use in the parent template %>
<% content_for :dropdown_data do %>
  {
    "dropdown_id": "<%= dropdown_id %>",
    "trigger_id": "<%= trigger_id %>",
    "menu_id": "<%= menu_id %>",
    "trigger_classes": "<%= trigger_classes %>",
    "menu_classes": "<%= menu_classes %>"
  }
<% end %>