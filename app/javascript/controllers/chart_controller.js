import { Controller } from "@hotwired/stimulus"
import { Chart, registerables } from "chart.js"

Chart.register(...registerables)

export default class extends Controller {
  static values = { 
    type: String,
    data: Object,
    label: String,
    colors: Array,
    options: Object
  }

  connect() {
    this.initializeChart();
  }

  disconnect() {
    if (this.chart) {
      this.chart.destroy()
    }
  }

  initializeChart() {
    const canvas = this.element.querySelector('canvas')
    if (!canvas) {
      console.error('Canvas element not found for chart')
      return
    }

    const ctx = canvas.getContext('2d')
    const chartData = this.prepareChartData()
    const chartOptions = this.prepareChartOptions()

    this.chart = new Chart(ctx, {
      type: this.typeValue || 'line',
      data: chartData,
      options: chartOptions
    })
  }

  prepareChartData() {
    const data = this.dataValue || {}
    const colors = this.colorsValue || this.defaultColors()
    
    // Handle empty data
    if (Object.keys(data).length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{
          label: 'No Data',
          data: [0],
          backgroundColor: `rgba(${colors[0]}, 0.3)`,
          borderColor: `rgb(${colors[0]})`,
          borderWidth: 1
        }]
      }
    }
    
    let datasets = []
    
    if (this.typeValue === 'line' || this.typeValue === 'bar') {
      datasets = [{
        label: this.labelValue || 'Data',
        data: Object.values(data),
        backgroundColor: this.typeValue === 'line' 
          ? `rgba(${colors[0]}, 0.1)`
          : `rgba(${colors[0]}, 0.8)`,
        borderColor: `rgb(${colors[0]})`,
        borderWidth: this.typeValue === 'bar' ? 1 : 2,
        tension: this.typeValue === 'line' ? 0.4 : 0,
        fill: this.typeValue === 'line'
      }]
    } else if (this.typeValue === 'doughnut' || this.typeValue === 'pie') {
      datasets = [{
        data: Object.values(data),
        backgroundColor: colors.map(c => `rgb(${c})`)
      }]
    }

    return {
      labels: Object.keys(data),
      datasets: datasets
    }
  }

  prepareChartOptions() {
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: this.typeValue === 'doughnut' || this.typeValue === 'pie',
          position: 'bottom'
        }
      }
    }

    if (this.typeValue === 'line' || this.typeValue === 'bar') {
      defaultOptions.scales = {
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }

    return { ...defaultOptions, ...(this.optionsValue || {}) }
  }

  defaultColors() {
    return [
      '99, 102, 241',   // Indigo
      '34, 197, 94',    // Green
      '251, 146, 60',   // Orange
      '239, 68, 68',    // Red
      '168, 85, 247',   // Purple
      '14, 165, 233',   // Sky Blue
      '236, 72, 153',   // Pink
      '245, 158, 11'    // Amber
    ]
  }
}