class AddByteSizeToSolidCacheEntries < ActiveRecord::Migration[7.1]
  def up
    # Add the byte_size column that solid_cache 1.0.7 requires
    add_column :solid_cache_entries, :byte_size, :integer, null: true
    
    # Clear existing cache entries since we need to recalculate byte_size
    # This is safe since cache is meant to be ephemeral
    execute "DELETE FROM solid_cache_entries"
    
    # Make byte_size not null after clearing
    change_column_null :solid_cache_entries, :byte_size, false
    
    # Add index on byte_size for performance (used for cache size calculations)
    add_index :solid_cache_entries, :byte_size
  end
  
  def down
    remove_index :solid_cache_entries, :byte_size if index_exists?(:solid_cache_entries, :byte_size)
    remove_column :solid_cache_entries, :byte_size
  end
end
