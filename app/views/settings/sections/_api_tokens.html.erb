<div id="api-tokens-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">API Tokens</h2>
      <p class="text-sm text-gray-600">Manage your API access tokens and integrations</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
      </svg>
    </div>
  </div>

  <!-- API Tokens List -->
  <div class="space-y-6">
    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Your API Tokens</h3>
        <button class="px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white text-sm font-medium rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg shadow-green-500/25">
          Generate New Token
        </button>
      </div>
      
      <div class="space-y-3">
        <div class="bg-white rounded-xl p-4 border border-green-200 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium text-gray-900">Production API Key</h4>
              <p class="text-sm text-gray-500">Created on January 15, 2024</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">Active</span>
              <button class="text-red-600 hover:text-red-700 text-sm font-medium">Revoke</button>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium text-gray-900">Development API Key</h4>
              <p class="text-sm text-gray-500">Created on December 8, 2023</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">Active</span>
              <button class="text-red-600 hover:text-red-700 text-sm font-medium">Revoke</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- API Documentation -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">API Documentation</h3>
      <p class="text-gray-600 mb-4">Learn how to integrate with our API and manage your links programmatically.</p>
      <div class="flex gap-3">
        <a href="#" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium">View Documentation</a>
        <a href="#" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">Download SDK</a>
      </div>
    </div>
  </div>
</div>