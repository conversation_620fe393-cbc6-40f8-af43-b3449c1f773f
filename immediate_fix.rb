#!/usr/bin/env ruby

# Immediate fix for solid_cache byte_size error
# This script provides a quick workaround while the proper migration is prepared

puts "🚨 IMMEDIATE FIX for solid_cache byte_size error"
puts "=" * 50

# Create a temporary patch for the User model
user_model_patch = <<~RUBY
  # Temporary patch for User model - add this to app/models/user.rb
  
  def total_clicks
    # Immediate fix: bypass caching entirely until schema is fixed
    links.joins(:link_clicks).count
  rescue => e
    Rails.logger.warn "Error calculating total clicks: \#{e.message}"
    0
  end
RUBY

puts "📝 STEP 1: Apply immediate patch to User model"
puts "Add this method to your User model (app/models/user.rb):"
puts user_model_patch

# Create a config patch to disable solid_cache temporarily
config_patch = <<~RUBY
  # Temporary patch for config/environments/development.rb
  # Add this line to disable solid_cache temporarily:
  
  config.cache_store = :memory_store
RUBY

puts "📝 STEP 2: Temporarily disable solid_cache"
puts "Add this to config/environments/development.rb:"
puts config_patch

puts "📝 STEP 3: Restart your Rails server"
puts "After making these changes, restart your server:"
puts "  rails server"

puts "\n✅ This will immediately fix the navbar error!"
puts "Then run the proper migration fix when ready:"
puts "  ruby fix_solid_cache.rb"

puts "\n🔄 To revert these temporary changes later:"
puts "1. Remove the temporary total_clicks method"
puts "2. Remove the config.cache_store line"
puts "3. Restart the server"
