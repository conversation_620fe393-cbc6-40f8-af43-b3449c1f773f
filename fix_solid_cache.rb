#!/usr/bin/env ruby

# Script to fix the solid_cache database schema issue
# Run this with: ruby fix_solid_cache.rb

puts "🔧 Fixing solid_cache database schema..."

# Check if we're in a Rails app
unless File.exist?('config/application.rb')
  puts "❌ Error: This doesn't appear to be a Rails application directory"
  exit 1
end

# Run all pending migrations
puts "📦 Running migrations to fix solid_cache schema..."
puts "   - Adding key_hash column..."
puts "   - Adding byte_size column..."
puts "   - Recreating table with correct schema..."
system("rails db:migrate")

if $?.success?
  puts "✅ Migration completed successfully!"
  
  # Test the cache functionality
  puts "🧪 Testing cache functionality..."
  
  # Create a simple test script
  test_script = <<~RUBY
    require_relative 'config/environment'
    
    begin
      # Test basic caching
      Rails.cache.write('test_key', 'test_value')
      result = Rails.cache.read('test_key')
      
      if result == 'test_value'
        puts "✅ Cache read/write test passed!"
      else
        puts "❌ Cache read/write test failed!"
        exit 1
      end
      
      # Test user total_clicks method if User model exists
      if defined?(User) && User.any?
        user = User.first
        clicks = user.total_clicks
        puts "✅ User total_clicks method working: #{clicks} clicks"
      else
        puts "ℹ️  No users found to test total_clicks method"
      end
      
      # Clean up test cache
      Rails.cache.delete('test_key')
      
      puts "🎉 All tests passed! The solid_cache issue has been resolved."
      
    rescue => e
      puts "❌ Error testing cache: #{e.message}"
      puts "   #{e.backtrace.first}"
      exit 1
    end
  RUBY
  
  File.write('test_cache.rb', test_script)
  system("ruby test_cache.rb")
  File.delete('test_cache.rb')
  
else
  puts "❌ Migration failed!"
  exit 1
end

puts "\n📋 Summary of changes made:"
puts "1. Added key_hash column to solid_cache_entries table"
puts "2. Added byte_size column to solid_cache_entries table"
puts "3. Recreated table with complete solid_cache 1.0.7 schema"
puts "4. Added comprehensive error handling to User#total_clicks method"
puts "5. Added schema validation before using cache"
puts "6. Added safer User#unique_visitors_count method"
puts "7. Implemented graceful fallback to direct queries"
puts "\n🚀 Your navbar should now work without database errors!"
