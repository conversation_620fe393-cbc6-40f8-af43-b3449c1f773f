<!DOCTYPE html>
<html>
<head>
  <title>Chart.js Debug Test</title>
  <script type="importmap">
    {
      "imports": {
        "chart.js": "/vendor/javascript/chart.js.js"
      }
    }
  </script>
</head>
<body>
  <h1>Chart.js Debug Test</h1>
  <canvas id="testChart" width="400" height="200"></canvas>
  
  <script type="module">
    import {
      Chart,
      CategoryScale,
      LinearScale,
      BarElement,
      Title,
      Tooltip,
      Legend
    } from "chart.js";
    
    console.log('Chart imported:', Chart);
    
    // Register components
    Chart.register(
      CategoryScale,
      LinearScale,
      BarElement,
      Title,
      Tooltip,
      Legend
    );
    
    const ctx = document.getElementById('testChart').getContext('2d');
    
    try {
      const chart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Red', 'Blue', 'Yellow'],
          datasets: [{
            label: 'Test Data',
            data: [12, 19, 3],
            backgroundColor: ['red', 'blue', 'yellow']
          }]
        },
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: 'Test Chart'
            }
          }
        }
      });
      console.log('Chart created successfully:', chart);
    } catch (error) {
      console.error('Error creating chart:', error);
    }
  </script>
</body>
</html>