<div id="integrations-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Integrations</h2>
      <p class="text-sm text-gray-600">Connect with your favorite tools and services</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
      </svg>
    </div>
  </div>

  <!-- Available Integrations -->
  <div class="space-y-6">
    <div class="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-100">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Available Integrations</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Slack Integration -->
        <div class="bg-white rounded-xl p-4 border border-emerald-200 shadow-sm">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52-2.523A2.528 2.528 0 0 1 5.042 10.12h2.52v2.522a2.528 2.528 0 0 1-2.52 2.523Zm0-6.584A2.528 2.528 0 0 1 2.522 6.058 2.528 2.528 0 0 1 5.042 3.535a2.528 2.528 0 0 1 2.52 2.523v2.523H5.042Zm6.584 0a2.528 2.528 0 0 1-2.523-2.523A2.528 2.528 0 0 1 11.626 3.535a2.528 2.528 0 0 1 2.523 2.523v2.523h-2.523Zm0 6.584a2.528 2.528 0 0 1 2.523 2.523 2.528 2.528 0 0 1-2.523 2.523H9.103v-2.523a2.528 2.528 0 0 1 2.523-2.523Z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Slack</h4>
                <p class="text-sm text-gray-500">Get notifications in Slack</p>
              </div>
            </div>
            <button class="px-3 py-1 bg-emerald-600 text-white text-sm font-medium rounded-lg hover:bg-emerald-700 transition-colors">
              Connect
            </button>
          </div>
        </div>
        
        <!-- Discord Integration -->
        <div class="bg-white rounded-xl p-4 border border-emerald-200 shadow-sm">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418Z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Discord</h4>
                <p class="text-sm text-gray-500">Share links in Discord</p>
              </div>
            </div>
            <button class="px-3 py-1 bg-emerald-600 text-white text-sm font-medium rounded-lg hover:bg-emerald-700 transition-colors">
              Connect
            </button>
          </div>
        </div>
        
        <!-- Zapier Integration -->
        <div class="bg-white rounded-xl p-4 border border-emerald-200 shadow-sm">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zM8.5 8.5L12 6l3.5 2.5L12 11 8.5 8.5zm7 7L12 18l-3.5-2.5L12 13l3.5 2.5z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Zapier</h4>
                <p class="text-sm text-gray-500">Automate workflows</p>
              </div>
            </div>
            <button class="px-3 py-1 bg-emerald-600 text-white text-sm font-medium rounded-lg hover:bg-emerald-700 transition-colors">
              Connect
            </button>
          </div>
        </div>
        
        <!-- Google Analytics Integration -->
        <div class="bg-white rounded-xl p-4 border border-emerald-200 shadow-sm">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Google Analytics</h4>
                <p class="text-sm text-gray-500">Track link performance</p>
              </div>
            </div>
            <span class="px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-lg">Connected</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Connected Integrations -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Connected Integrations</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl border border-green-200">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            </div>
            <div>
              <div class="font-medium text-gray-900">Google Analytics</div>
              <div class="text-sm text-gray-500">Connected on Jan 15, 2024</div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">Active</span>
            <button class="text-red-600 hover:text-red-700 text-sm font-medium">Disconnect</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Webhooks -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Webhooks</h3>
        <button class="px-4 py-2 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white text-sm font-medium rounded-xl hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 shadow-lg shadow-emerald-500/25">
          Add Webhook
        </button>
      </div>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
          <div>
            <div class="font-medium text-gray-900">https://api.myapp.com/webhooks/links</div>
            <div class="text-sm text-gray-500">Triggers on link creation and clicks</div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">Active</span>
            <button class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>