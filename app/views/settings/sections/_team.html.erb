<div id="team-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Team Management</h2>
      <p class="text-sm text-gray-600">Manage team members and permissions</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
      </svg>
    </div>
  </div>

  <!-- Team Overview -->
  <div class="space-y-6">
    <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-6 border border-purple-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Team Overview</h3>
        <button class="px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg shadow-purple-500/25">
          Invite Member
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-white rounded-xl p-4 border border-purple-200 shadow-sm">
          <div class="text-2xl font-bold text-gray-900">5</div>
          <div class="text-sm text-gray-500">team members</div>
        </div>
        <div class="bg-white rounded-xl p-4 border border-purple-200 shadow-sm">
          <div class="text-2xl font-bold text-gray-900">3</div>
          <div class="text-sm text-gray-500">active projects</div>
        </div>
        <div class="bg-white rounded-xl p-4 border border-purple-200 shadow-sm">
          <div class="text-2xl font-bold text-gray-900">2</div>
          <div class="text-sm text-gray-500">pending invites</div>
        </div>
      </div>
    </div>
    
    <!-- Team Members -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Team Members</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
              <span class="text-white font-medium text-sm">JD</span>
            </div>
            <div>
              <div class="font-medium text-gray-900">John Doe</div>
              <div class="text-sm text-gray-500"><EMAIL></div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-lg">Owner</span>
            <button class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
              <span class="text-white font-medium text-sm">JS</span>
            </div>
            <div>
              <div class="font-medium text-gray-900">Jane Smith</div>
              <div class="text-sm text-gray-500"><EMAIL></div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">Admin</span>
            <button class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
              <span class="text-white font-medium text-sm">MB</span>
            </div>
            <div>
              <div class="font-medium text-gray-900">Mike Brown</div>
              <div class="text-sm text-gray-500"><EMAIL></div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-lg">Member</span>
            <button class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Pending Invitations -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Pending Invitations</h3>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-xl border border-yellow-200">
          <div>
            <div class="font-medium text-gray-900"><EMAIL></div>
            <div class="text-sm text-gray-500">Invited 2 days ago</div>
          </div>
          <div class="flex items-center gap-2">
            <button class="px-3 py-1 text-blue-600 hover:text-blue-700 text-sm font-medium">Resend</button>
            <button class="px-3 py-1 text-red-600 hover:text-red-700 text-sm font-medium">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>