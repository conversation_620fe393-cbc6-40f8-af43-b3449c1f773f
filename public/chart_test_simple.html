<!DOCTYPE html>
<html>
<head>
  <title>Chart.js Test</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
</head>
<body>
  <h1>Chart.js Test</h1>
  <canvas id="testChart" width="400" height="200"></canvas>
  
  <script>
    console.log('Chart from global:', window.Chart);
    const Chart = window.Chart;
    
    const ctx = document.getElementById('testChart').getContext('2d');
    
    try {
      const chart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Red', 'Blue', 'Yellow'],
          datasets: [{
            label: 'Test Data',
            data: [12, 19, 3],
            backgroundColor: ['red', 'blue', 'yellow']
          }]
        },
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: 'Test Chart'
            }
          }
        }
      });
      console.log('Chart created successfully:', chart);
      document.body.innerHTML += '<p style="color: green;">✅ Chart.js is working!</p>';
    } catch (error) {
      console.error('Error creating chart:', error);
      document.body.innerHTML += '<p style="color: red;">❌ Chart.js failed: ' + error.message + '</p>';
    }
  </script>
</body>
</html>