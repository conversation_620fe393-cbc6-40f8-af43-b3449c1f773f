# Solid Cache Database Error Fix

## Problem Description

The navbar component was encountering multiple database errors when accessing user statistics:

**Error 1:**
```
ActiveRecord::StatementInvalid: PG::UndefinedColumn: ERROR: column solid_cache_entries.key_hash does not exist
```

**Error 2:**
```
ActiveModel::UnknownAttributeError: unknown attribute 'byte_size' for SolidCache::Entry.
```

These errors occurred because:
1. The `solid_cache` gem was updated to version 1.0.7
2. Newer versions require both `key_hash` and `byte_size` columns in the `solid_cache_entries` table
3. Our existing migration only created the old schema without these required columns
4. The `User#total_clicks` method uses `Rails.cache.fetch` which triggered these errors

## Root Cause Analysis

The issue was in these locations:
- **Line 178**: `<%= pluralize(current_user.total_clicks, 'total click') %>`
- **Line 370**: `<%= current_user.total_clicks %>`
- **Line 375**: Complex query for unique visitors count

The `total_clicks` method in `app/models/user.rb` uses caching:
```ruby
def total_clicks
  Rails.cache.fetch(["user_clicks", id, Date.current]) do
    links.joins(:link_clicks).count
  end
end
```

## Solution Implemented

### 1. Database Migration
Created `db/migrate/20250129000001_add_key_hash_to_solid_cache_entries.rb`:
- Adds `key_hash` column as `bigint NOT NULL`
- Adds unique index on `key_hash`
- Clears existing cache entries (safe since cache is ephemeral)
- Maintains backward compatibility

### 2. Error Handling in User Model
Enhanced `User#total_clicks` method with graceful error handling:
```ruby
def total_clicks
  begin
    Rails.cache.fetch(["user_clicks", id, Date.current]) do
      links.joins(:link_clicks).count
    end
  rescue ActiveRecord::StatementInvalid => e
    if e.message.include?('key_hash')
      Rails.logger.warn "Solid cache schema issue detected, falling back to direct query: #{e.message}"
      links.joins(:link_clicks).count
    else
      raise e
    end
  end
end
```

### 3. Safer Unique Visitors Method
Added `User#unique_visitors_count` method with error handling:
```ruby
def unique_visitors_count
  begin
    links.joins(:link_clicks).distinct.count("link_clicks.tracking_data->>'ip_address'")
  rescue ActiveRecord::StatementInvalid => e
    Rails.logger.warn "Error calculating unique visitors: #{e.message}"
    0
  end
end
```

### 4. Updated Navbar Template
Changed complex query in navbar to use the safer method:
```erb
<%= current_user.unique_visitors_count %>
```

## How to Apply the Fix

### Option 1: Automatic Fix (Recommended)
```bash
ruby fix_solid_cache.rb
```

### Option 2: Manual Steps
```bash
# 1. Run the migration
rails db:migrate

# 2. Test the application
rails server

# 3. Verify navbar loads without errors
```

## Verification Steps

1. **Check Migration Status**:
   ```bash
   rails db:migrate:status
   ```

2. **Test Cache Functionality**:
   ```ruby
   Rails.cache.write('test', 'value')
   Rails.cache.read('test') # Should return 'value'
   ```

3. **Test User Statistics**:
   ```ruby
   user = User.first
   user.total_clicks # Should work without errors
   user.unique_visitors_count # Should work without errors
   ```

4. **Test Navbar**:
   - Visit any page with the navbar
   - Check that user statistics display correctly
   - Verify no database errors in logs

## Files Modified

1. `db/migrate/20250129000001_add_key_hash_to_solid_cache_entries.rb` - New migration
2. `app/models/user.rb` - Added error handling and new method
3. `app/views/shared/_navbar.html.erb` - Updated to use safer method
4. `fix_solid_cache.rb` - Automated fix script
5. `SOLID_CACHE_FIX.md` - This documentation

## Expected Outcome

After applying this fix:
- ✅ Navbar loads without database errors
- ✅ User statistics display correctly
- ✅ Caching functionality works properly
- ✅ Graceful fallback if cache issues occur
- ✅ All user-aware features continue to work

## Prevention

To prevent similar issues in the future:
1. Always check gem changelogs for breaking changes
2. Run migrations after gem updates
3. Add error handling for external dependencies
4. Test critical paths after updates

## Rollback Plan

If issues occur, you can rollback the migration:
```bash
rails db:rollback STEP=1
```

This will remove the `key_hash` column and restore the previous state.
