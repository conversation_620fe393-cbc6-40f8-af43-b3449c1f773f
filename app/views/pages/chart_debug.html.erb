<% content_for :page_title, "Chart Debug" %>

<div class="p-6">
  <h1 class="text-2xl font-bold mb-6">Chart Debug Page</h1>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Line Chart Test -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-lg font-semibold mb-4">Line Chart Test</h2>
      <%= line_chart({"Jan" => 10, "Feb" => 20, "Mar" => 15, "Apr" => 25}, label: 'Test Data', height: '200px') %>
    </div>
    
    <!-- Bar Chart Test -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-lg font-semibold mb-4">Bar Chart Test</h2>
      <%= bar_chart({"A" => 5, "B" => 10, "C" => 8, "D" => 12}, label: 'Test Data', height: '200px') %>
    </div>
    
    <!-- Pie Chart Test -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-lg font-semibold mb-4">Pie Chart Test</h2>
      <%= pie_chart({"Red" => 30, "Blue" => 20, "Green" => 25, "Yellow" => 25}, height: '200px') %>
    </div>
    
    <!-- Doughnut Chart Test -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-lg font-semibold mb-4">Doughnut Chart Test</h2>
      <%= doughnut_chart({"Desktop" => 60, "Mobile" => 30, "Tablet" => 10}, height: '200px') %>
    </div>
  </div>
  
  <div class="mt-6">
    <p class="text-sm text-gray-600">If you can see charts above, the Chart.js integration is working correctly.</p>
  </div>
</div>