<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-8">Chart Test Page</h1>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Line Chart -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Line Chart Test</h2>
      <%= chart_container(
        type: 'line',
        data: { 'Jan' => 10, 'Feb' => 20, 'Mar' => 15, 'Apr' => 25, 'May' => 30 },
        label: 'Test Data',
        colors: chart_colors(1)
      ) %>
    </div>
    
    <!-- Bar Chart -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Bar Chart Test</h2>
      <%= chart_container(
        type: 'bar',
        data: { 'A' => 5, 'B' => 10, 'C' => 8, 'D' => 12 },
        label: 'Test Categories',
        colors: chart_colors(1)
      ) %>
    </div>
    
    <!-- Pie Chart -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Pie Chart Test</h2>
      <%= chart_container(
        type: 'pie',
        data: { 'Red' => 30, 'Blue' => 25, 'Green' => 20, 'Yellow' => 25 },
        colors: chart_colors(4)
      ) %>
    </div>
    
    <!-- Doughnut Chart -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Doughnut Chart Test</h2>
      <%= chart_container(
        type: 'doughnut',
        data: { 'Desktop' => 60, 'Mobile' => 35, 'Tablet' => 5 },
        colors: chart_colors(3)
      ) %>
    </div>
  </div>
</div>