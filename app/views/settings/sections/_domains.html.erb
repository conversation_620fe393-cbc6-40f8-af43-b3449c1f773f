<div id="domains-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Custom Domains</h2>
      <p class="text-sm text-gray-600">Manage your custom domains and SSL certificates</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
      </svg>
    </div>
  </div>

  <!-- Add Domain -->
  <div class="space-y-6">
    <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-6 border border-indigo-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Add New Domain</h3>
      </div>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Domain Name</label>
          <div class="flex gap-3">
            <input type="text" placeholder="example.com" class="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <button class="px-6 py-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-lg shadow-indigo-500/25">
              Add Domain
            </button>
          </div>
        </div>
        
        <div class="bg-blue-50 rounded-xl p-4 border border-blue-200">
          <div class="flex items-start gap-3">
            <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
              <h4 class="font-medium text-blue-900 mb-1">Domain Setup Instructions</h4>
              <p class="text-sm text-blue-700">After adding your domain, you'll need to configure DNS records to point to our servers. We'll provide detailed instructions.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Active Domains -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Active Domains</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl border border-green-200">
          <div class="flex items-center gap-3">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <div>
              <div class="font-medium text-gray-900">links.mycompany.com</div>
              <div class="text-sm text-gray-500">SSL Certificate: Valid until Dec 2024</div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">Active</span>
            <button class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-blue-50 rounded-xl border border-blue-200">
          <div class="flex items-center gap-3">
            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
            <div>
              <div class="font-medium text-gray-900">go.example.org</div>
              <div class="text-sm text-gray-500">SSL Certificate: Valid until Mar 2024</div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-lg">Active</span>
            <button class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Pending Domains -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Pending Verification</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-xl border border-yellow-200">
          <div class="flex items-center gap-3">
            <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
            <div>
              <div class="font-medium text-gray-900">short.newdomain.com</div>
              <div class="text-sm text-gray-500">Waiting for DNS propagation</div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs font-medium rounded-lg">Pending</span>
            <button class="px-3 py-1 text-blue-600 hover:text-blue-700 text-sm font-medium">Check Status</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Domain Statistics -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Domain Statistics</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-4 border border-indigo-200">
          <div class="text-2xl font-bold text-indigo-900">2</div>
          <div class="text-sm text-indigo-700">active domains</div>
        </div>
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
          <div class="text-2xl font-bold text-green-900">1,247</div>
          <div class="text-sm text-green-700">links created</div>
        </div>
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
          <div class="text-2xl font-bold text-blue-900">98.9%</div>
          <div class="text-sm text-blue-700">uptime</div>
        </div>
      </div>
    </div>
  </div>
</div>