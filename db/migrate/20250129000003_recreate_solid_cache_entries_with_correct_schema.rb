class RecreateSolidCacheEntriesWithCorrectSchema < ActiveRecord::Migration[7.1]
  def up
    # Drop the existing table and recreate with the correct schema for solid_cache 1.0.7
    drop_table :solid_cache_entries if table_exists?(:solid_cache_entries)
    
    create_table :solid_cache_entries do |t|
      t.binary :key, null: false, limit: 1024
      t.binary :value, null: false, limit: 512.megabytes
      t.datetime :created_at, null: false
      t.bigint :key_hash, null: false
      t.integer :byte_size, null: false
      
      # Indexes for performance
      t.index :key, unique: true
      t.index :key_hash, unique: true
      t.index :created_at
      t.index :byte_size
    end
  end
  
  def down
    # Restore the original schema if needed
    drop_table :solid_cache_entries if table_exists?(:solid_cache_entries)
    
    create_table :solid_cache_entries do |t|
      t.binary :key, null: false, limit: 1024
      t.binary :value, null: false, limit: 512.megabytes
      t.datetime :created_at, null: false
      
      t.index :key, unique: true
      t.index :created_at
    end
  end
end
