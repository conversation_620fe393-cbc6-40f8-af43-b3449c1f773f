<div id="billing-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Billing & Subscription</h2>
      <p class="text-sm text-gray-600">Manage your subscription and billing information</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
      </svg>
    </div>
  </div>

  <!-- Current Plan -->
  <div class="space-y-6">
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Current Plan</h3>
        <span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm font-medium rounded-lg">Pro Plan</span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white rounded-xl p-4 border border-blue-200 shadow-sm">
          <div class="text-2xl font-bold text-gray-900">$29</div>
          <div class="text-sm text-gray-500">per month</div>
        </div>
        <div class="bg-white rounded-xl p-4 border border-blue-200 shadow-sm">
          <div class="text-2xl font-bold text-gray-900">10,000</div>
          <div class="text-sm text-gray-500">links per month</div>
        </div>
        <div class="bg-white rounded-xl p-4 border border-blue-200 shadow-sm">
          <div class="text-2xl font-bold text-gray-900">∞</div>
          <div class="text-sm text-gray-500">custom domains</div>
        </div>
      </div>
      
      <div class="flex gap-3">
        <button class="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg shadow-blue-500/25">
          Upgrade Plan
        </button>
        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors text-sm font-medium">
          View All Plans
        </button>
      </div>
    </div>
    
    <!-- Payment Method -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Payment Method</h3>
        <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">Update</button>
      </div>
      
      <div class="flex items-center gap-4">
        <div class="w-12 h-8 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
          <span class="text-white text-xs font-bold">VISA</span>
        </div>
        <div>
          <div class="font-medium text-gray-900">•••• •••• •••• 4242</div>
          <div class="text-sm text-gray-500">Expires 12/25</div>
        </div>
      </div>
    </div>
    
    <!-- Billing History -->
    <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">Billing History</h3>
        <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">View All</button>
      </div>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <div>
            <div class="font-medium text-gray-900">Pro Plan - January 2024</div>
            <div class="text-sm text-gray-500">Paid on Jan 1, 2024</div>
          </div>
          <div class="text-right">
            <div class="font-medium text-gray-900">$29.00</div>
            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-lg">Paid</span>
          </div>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <div>
            <div class="font-medium text-gray-900">Pro Plan - December 2023</div>
            <div class="text-sm text-gray-500">Paid on Dec 1, 2023</div>
          </div>
          <div class="text-right">
            <div class="font-medium text-gray-900">$29.00</div>
            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-lg">Paid</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>