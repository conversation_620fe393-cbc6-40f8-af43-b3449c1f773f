<%= render 'shared/dropdown_styles' %>
<nav class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-lg border-b border-gray-200/50" data-controller="navbar">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- <PERSON><PERSON> and Brand -->
      <div class="flex items-center">
        <%= link_to root_path, class: "flex items-center space-x-3 group" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-transform duration-200">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
          </div>
          <span class="text-xl font-bold text-gray-900">LinkMaster</span>
        <% end %>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        <%= link_to "Features", root_path(anchor: "features"), class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
        <%= link_to "Pricing", root_path(anchor: "pricing"), class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
        
        <!-- Unified Resources Dropdown -->
        <div class="relative" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
          <button data-dropdown-target="trigger" data-action="click->dropdown#toggle click@window->dropdown#hide" 
                  class="group flex items-center space-x-2 px-3 py-2.5 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300 hover:shadow-lg">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 rounded-lg flex items-center justify-center transition-all duration-200">
                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              </div>
              <span class="text-gray-600 group-hover:text-purple-700 font-medium transition-colors">Resources</span>
            </div>
            <svg class="w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          
          <div data-dropdown-target="menu" class="hidden absolute right-0 mt-3 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden z-50" role="menu">
            <!-- Resources Header -->
            <div class="px-6 py-4 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600">
              <h3 class="text-lg font-semibold text-white mb-1">Learning Resources</h3>
              <p class="text-purple-100 text-sm">Everything you need to succeed with LinkMaster</p>
            </div>
            
            <!-- Resources Grid -->
            <div class="p-2">
              <div class="grid grid-cols-2 gap-2 p-2">
                <%= link_to blog_path, class: "group p-4 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-200" do %>
                  <div class="flex flex-col items-center text-center space-y-2">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 rounded-xl flex items-center justify-center transition-all duration-200">
                      <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 group-hover:text-purple-700">Blog</p>
                      <p class="text-xs text-gray-500 group-hover:text-purple-500">Latest insights</p>
                    </div>
                  </div>
                <% end %>
                
                <%= link_to help_path, class: "group p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 transition-all duration-200" do %>
                  <div class="flex flex-col items-center text-center space-y-2">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-cyan-100 group-hover:from-blue-200 group-hover:to-cyan-200 rounded-xl flex items-center justify-center transition-all duration-200">
                      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 group-hover:text-blue-700">Help Center</p>
                      <p class="text-xs text-gray-500 group-hover:text-blue-500">Get support</p>
                    </div>
                  </div>
                <% end %>
                
                <%= link_to guides_path, class: "group p-4 rounded-xl hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 transition-all duration-200" do %>
                  <div class="flex flex-col items-center text-center space-y-2">
                    <div class="w-12 h-12 bg-gradient-to-br from-emerald-100 to-teal-100 group-hover:from-emerald-200 group-hover:to-teal-200 rounded-xl flex items-center justify-center transition-all duration-200">
                      <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 group-hover:text-emerald-700">Guides</p>
                      <p class="text-xs text-gray-500 group-hover:text-emerald-500">Learn more</p>
                    </div>
                  </div>
                <% end %>
                
                <%= link_to webinars_path, class: "group p-4 rounded-xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-200" do %>
                  <div class="flex flex-col items-center text-center space-y-2">
                    <div class="w-12 h-12 bg-gradient-to-br from-indigo-100 to-purple-100 group-hover:from-indigo-200 group-hover:to-purple-200 rounded-xl flex items-center justify-center transition-all duration-200">
                      <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 group-hover:text-indigo-700">Webinars</p>
                      <p class="text-xs text-gray-500 group-hover:text-indigo-500">Live sessions</p>
                    </div>
                  </div>
                <% end %>
              </div>
              
              <!-- Divider -->
              <div class="my-2 mx-4 border-t border-gray-100"></div>
              
              <!-- Featured Section -->
              <div class="px-4 pb-4">
                <%= link_to case_studies_path, class: "group flex items-center p-4 rounded-xl bg-gradient-to-r from-orange-50 to-pink-50 hover:from-orange-100 hover:to-pink-100 transition-all duration-200" do %>
                  <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-orange-400 to-pink-400 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <p class="font-semibold text-gray-900">Customer Success Stories</p>
                    <p class="text-sm text-gray-600 mt-1">See how companies achieve 300%+ ROI</p>
                  </div>
                  <svg class="w-5 h-5 text-orange-400 group-hover:text-orange-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
        <%= link_to "About", about_path, class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
        <%= link_to "Contact", contact_path, class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
      </div>

      <!-- Desktop Auth/User Menu -->
      <div class="hidden md:flex items-center space-x-4">
        <% if user_signed_in? %>
          <!-- Unified Notifications Dropdown -->
          <div class="relative" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
            <button data-dropdown-target="trigger" data-action="click->dropdown#toggle click@window->dropdown#hide" 
                    class="group flex items-center space-x-2 px-3 py-2.5 rounded-xl hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50 transition-all duration-300 hover:shadow-lg">
              <div class="flex items-center space-x-2">
                <div class="relative w-8 h-8 bg-gradient-to-br from-yellow-100 to-orange-100 group-hover:from-yellow-200 group-hover:to-orange-200 rounded-lg flex items-center justify-center transition-all duration-200">
                  <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                  </svg>
                  <span class="absolute -top-1 -right-1 flex h-3 w-3">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                  </span>
                </div>
                <span class="hidden lg:block text-gray-600 group-hover:text-orange-700 font-medium transition-colors">Notifications</span>
              </div>
              <svg class="hidden lg:block w-4 h-4 text-gray-400 group-hover:text-orange-600 transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <!-- Unified Notifications Menu -->
            <div data-dropdown-target="menu" class="hidden absolute right-0 mt-3 w-96 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden z-50" role="menu">
              <!-- Notifications Header -->
              <div class="px-6 py-4 bg-gradient-to-br from-yellow-500 via-orange-500 to-red-500">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="text-lg font-semibold text-white">Notifications</h3>
                    <p class="text-yellow-100 text-sm">You have 3 unread messages</p>
                  </div>
                  <button class="text-white/80 hover:text-white transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                  </button>
                </div>
              </div>
              
              <!-- Notifications List -->
              <div class="max-h-96 overflow-y-auto divide-y divide-gray-100">
                <!-- Notification 1 -->
                <div class="notification-item unread px-4 py-4 hover:bg-gray-50 transition-colors cursor-pointer" role="menuitem">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-sm">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </div>
                    </div>
                    <div class="notification-content flex-1 min-w-0">
                      <p class="notification-title font-medium text-gray-900 text-sm leading-5">Link Performance Alert</p>
                      <p class="notification-description text-sm text-gray-600 mt-1 leading-5">Your link "Summer Campaign" just hit 10K clicks! 🎉</p>
                      <p class="notification-time text-xs text-gray-400 mt-2">2 minutes ago</p>
                    </div>
                    <button class="flex-shrink-0 ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Notification 2 -->
                <div class="notification-item px-4 py-4 hover:bg-gray-50 transition-colors cursor-pointer" role="menuitem">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-sm">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                      </div>
                    </div>
                    <div class="notification-content flex-1 min-w-0">
                      <p class="notification-title font-medium text-gray-900 text-sm leading-5">Weekly Analytics Report</p>
                      <p class="notification-description text-sm text-gray-600 mt-1 leading-5">Your weekly performance report is ready to view</p>
                      <p class="notification-time text-xs text-gray-400 mt-2">1 hour ago</p>
                    </div>
                    <button class="flex-shrink-0 ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Notification 3 -->
                <div class="notification-item px-4 py-4 hover:bg-gray-50 transition-colors cursor-pointer" role="menuitem">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center shadow-sm">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                        </svg>
                      </div>
                    </div>
                    <div class="notification-content flex-1 min-w-0">
                      <p class="notification-title font-medium text-gray-900 text-sm leading-5">New Feature Available</p>
                      <p class="notification-description text-sm text-gray-600 mt-1 leading-5">QR Code customization is now live! Try it out</p>
                      <p class="notification-time text-xs text-gray-400 mt-2">3 hours ago</p>
                    </div>
                    <button class="flex-shrink-0 ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Notification 4 -->
                <div class="notification-item px-4 py-4 hover:bg-gray-50 transition-colors cursor-pointer" role="menuitem">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 bg-gradient-to-br from-red-400 to-orange-500 rounded-full flex items-center justify-center shadow-sm">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                      </div>
                    </div>
                    <div class="notification-content flex-1 min-w-0">
                      <p class="notification-title font-medium text-gray-900 text-sm leading-5">Usage Limit Warning</p>
                      <p class="notification-description text-sm text-gray-600 mt-1 leading-5">You've used 80% of your monthly link quota</p>
                      <p class="notification-time text-xs text-gray-400 mt-2">Yesterday</p>
                    </div>
                    <button class="flex-shrink-0 ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Footer -->
              <div class="px-4 py-3 bg-gray-50/50 border-t border-gray-100">
                <button class="w-full py-2 px-4 text-center text-sm font-medium text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-all duration-200">
                  View All Notifications
                </button>
              </div>
            </div>
          </div>

          <!-- Unified User Dropdown -->
          <div class="relative" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
            <!-- User Avatar Button with Unified Styling -->
            <button data-dropdown-target="trigger" data-action="click->dropdown#toggle click@window->dropdown#hide" 
                    class="group flex items-center space-x-2 px-3 py-2.5 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300 hover:shadow-lg">
              <div class="flex items-center space-x-2">
                <div class="relative w-8 h-8 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600 rounded-lg flex items-center justify-center text-white font-semibold transition-all duration-200">
                  <%= current_user.email[0].upcase %>
                  <!-- Online Status Indicator -->
                  <div class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 border border-white rounded-full"></div>
                </div>
                <div class="hidden lg:block text-left">
                  <p class="text-sm font-medium text-gray-600 group-hover:text-purple-700 transition-colors">
                    <%= current_user.email.split('@').first.titleize %>
                  </p>
                  <p class="text-xs text-gray-500 group-hover:text-purple-500 transition-colors">Free Plan</p>
                </div>
              </div>
              <svg class="hidden lg:block w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Unified Dropdown Menu -->
            <div data-dropdown-target="menu" class="hidden absolute right-0 mt-3 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden z-50" role="menu">
              <!-- User Profile Header -->
              <div class="relative px-6 py-5 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600">
                <div class="flex items-center space-x-4">
                  <div class="relative">
                    <div class="w-14 h-14 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                      <%= current_user.email[0].upcase %>
                    </div>
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-semibold text-white truncate">
                      <%= current_user.email.split('@').first.titleize %>
                    </h3>
                    <p class="text-purple-100 text-sm truncate"><%= current_user.email %></p>
                    <div class="flex items-center mt-2 space-x-3">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/20 text-white">
                        Free Plan
                      </span>
                      <span class="text-purple-100 text-xs">245 links used</span>
                    </div>
                  </div>
                </div>
                
                <!-- Upgrade Banner -->
                <div class="mt-4 p-3 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-white text-sm font-medium">Upgrade to Pro</p>
                      <p class="text-purple-100 text-xs">Unlock unlimited links & analytics</p>
                    </div>
                    <button class="px-3 py-1.5 bg-white text-purple-600 rounded-lg text-xs font-medium hover:bg-purple-50 transition-colors">
                      Upgrade
                    </button>
                  </div>
                </div>
              </div>

              <!-- Navigation Menu -->
              <div class="py-2">
                <!-- Primary Actions -->
                <div class="px-2">
                  <%= link_to authenticated_root_path, class: "dropdown-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 rounded-xl mr-3 transition-all duration-200">
                      <svg class="dropdown-icon w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                      </svg>
                    </div>
                    <div class="flex-1">
                      <p class="font-medium">Dashboard</p>
                      <p class="text-xs text-gray-500 group-hover:text-purple-500">Overview & quick actions</p>
                    </div>
                    <svg class="w-4 h-4 text-gray-400 group-hover:text-purple-600 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  <% end %>
                  
                  <%= link_to links_path, class: "dropdown-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 hover:text-blue-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-100 to-cyan-100 group-hover:from-blue-200 group-hover:to-cyan-200 rounded-xl mr-3 transition-all duration-200">
                      <svg class="dropdown-icon w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                      </svg>
                    </div>
                    <div class="flex-1">
                      <p class="font-medium">My Links</p>
                      <p class="text-xs text-gray-500 group-hover:text-blue-500">Manage & organize links</p>
                    </div>
                    <div class="flex items-center space-x-2">
                      <% if user_signed_in? %>
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"><%= current_user.links.count %></span>
                      <% end %>
                      <svg class="w-4 h-4 text-gray-400 group-hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </div>
                  <% end %>
                  
                  <%= link_to analytics_path, class: "dropdown-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 hover:text-emerald-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-emerald-100 to-teal-100 group-hover:from-emerald-200 group-hover:to-teal-200 rounded-xl mr-3 transition-all duration-200">
                      <svg class="dropdown-icon w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    </div>
                    <div class="flex-1">
                      <p class="font-medium">Analytics</p>
                      <p class="text-xs text-gray-500 group-hover:text-emerald-500">Performance insights</p>
                    </div>
                    <svg class="w-4 h-4 text-gray-400 group-hover:text-emerald-600 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  <% end %>
                </div>

                <div class="dropdown-divider"></div>

                <!-- Secondary Actions -->
                <div class="px-2">
                  <%= link_to settings_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span class="font-medium">Settings</span>
                  <% end %>
                  
                  <%= link_to "#", class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"></path>
                    </svg>
                    <span class="font-medium">Billing & Usage</span>
                  <% end %>
                  
                  <%= link_to "#", class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                    <span class="font-medium">Help & Support</span>
                  <% end %>
                </div>

                <div class="dropdown-divider"></div>

                <!-- Sign Out -->
                <div class="px-2 pb-2">
                  <%= link_to destroy_user_session_path, method: :delete, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                    <svg class="dropdown-icon w-5 h-5 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    <span class="font-medium">Sign Out</span>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <%= link_to "Sign In", new_user_session_path, class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
          <%= link_to "Get Started", new_user_registration_path, class: "bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-200" %>
        <% end %>
      </div>

      <!-- Unified Mobile Menu Button -->
      <div class="md:hidden" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
        <button data-dropdown-target="trigger" data-action="click->dropdown#toggle click@window->dropdown#hide" 
                class="group flex items-center space-x-2 px-3 py-2.5 rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100 transition-all duration-300 hover:shadow-lg">
          <div class="flex items-center space-x-2">
            <div class="relative w-8 h-8 bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 rounded-lg flex items-center justify-center transition-all duration-200">
              <!-- Hamburger Icon -->
              <div class="relative w-4 h-3 flex flex-col justify-between">
                <span class="block h-0.5 w-full bg-purple-600 rounded-full transform transition-all duration-300 origin-left group-hover:rotate-[5deg]"></span>
                <span class="block h-0.5 w-full bg-purple-600 rounded-full transform transition-all duration-300 group-hover:scale-x-75"></span>
                <span class="block h-0.5 w-full bg-purple-600 rounded-full transform transition-all duration-300 origin-left group-hover:rotate-[-5deg]"></span>
              </div>
              <!-- Notification Badge -->
              <span class="absolute -top-1 -right-1 flex h-3 w-3">
                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                <span class="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
              </span>
            </div>
            <span class="text-gray-600 group-hover:text-purple-700 font-medium transition-colors">Menu</span>
          </div>
        </button>
        
        <!-- Mobile Dropdown Menu -->
        <div data-dropdown-target="menu" class="hidden absolute right-0 mt-3 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden z-50" role="menu">
          <!-- Mobile Menu Header -->
          <div class="px-6 py-4 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600">
            <h3 class="text-lg font-semibold text-white mb-1">Navigation</h3>
            <p class="text-purple-100 text-sm">Quick access to all features</p>
          </div>
          
          <!-- Mobile Menu Content -->
          <div class="py-2">
            <!-- Primary Navigation -->
            <div class="px-2">
              <%= link_to root_path, class: "dropdown-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 rounded-xl mr-3 transition-all duration-200">
                  <svg class="dropdown-icon w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                  </svg>
                </div>
                <span class="font-medium">Home</span>
              <% end %>
              
              <%= link_to root_path(anchor: "features"), class: "dropdown-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 hover:text-blue-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-100 to-cyan-100 group-hover:from-blue-200 group-hover:to-cyan-200 rounded-xl mr-3 transition-all duration-200">
                  <svg class="dropdown-icon w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <span class="font-medium">Features</span>
              <% end %>
              
              <%= link_to root_path(anchor: "pricing"), class: "dropdown-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 hover:text-emerald-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-emerald-100 to-teal-100 group-hover:from-emerald-200 group-hover:to-teal-200 rounded-xl mr-3 transition-all duration-200">
                  <svg class="dropdown-icon w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <span class="font-medium">Pricing</span>
              <% end %>
            </div>
            
            <div class="dropdown-divider"></div>
            
            <!-- Resources Section -->
            <div class="px-2">
              <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Resources</div>
              
              <%= link_to blog_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                </svg>
                <span class="font-medium">Blog</span>
              <% end %>
              
              <%= link_to help_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-medium">Help Center</span>
              <% end %>
              
              <%= link_to guides_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <span class="font-medium">Guides</span>
              <% end %>
            </div>
            
            <div class="dropdown-divider"></div>
            
            <!-- Auth Section -->
            <div class="px-2 pb-2">
              <% if user_signed_in? %>
                <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</div>
                
                <%= link_to authenticated_root_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                  <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2H3z"></path>
                  </svg>
                  <span class="font-medium">Dashboard</span>
                <% end %>
                
                <%= link_to settings_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                  <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="font-medium">Settings</span>
                <% end %>
                
                <%= link_to destroy_user_session_path, method: :delete, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                  <svg class="dropdown-icon w-5 h-5 text-red-500 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  <span class="font-medium">Sign Out</span>
                <% end %>
              <% else %>
                <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Get Started</div>
                
                <%= link_to new_user_session_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mx-2", role: "menuitem" do %>
                  <svg class="dropdown-icon w-5 h-5 text-gray-400 group-hover:text-gray-600 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  <span class="font-medium">Sign In</span>
                <% end %>
                
                <%= link_to new_user_registration_path, class: "dropdown-item group flex items-center px-4 py-2.5 rounded-xl text-purple-600 hover:bg-purple-50 hover:text-purple-700 transition-all duration-200 mx-2", role: "menuitem" do %>
                  <svg class="dropdown-icon w-5 h-5 text-purple-500 mr-3 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                  </svg>
                  <span class="font-medium">Get Started</span>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


</nav>

<!-- Spacer for fixed navbar -->
<div class="h-16"></div>