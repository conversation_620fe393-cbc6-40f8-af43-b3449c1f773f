#!/bin/bash

# Solid Cache Fix Script
# This script fixes the missing key_hash column issue in solid_cache_entries table

echo "🔧 LinkMaster Solid Cache Fix"
echo "=============================="
echo ""

# Check if we're in the right directory
if [ ! -f "config/application.rb" ]; then
    echo "❌ Error: Please run this script from the Rails application root directory"
    exit 1
fi

echo "📋 Checking current database status..."

# Check if the migration exists
if [ ! -f "db/migrate/20250129000001_add_key_hash_to_solid_cache_entries.rb" ]; then
    echo "❌ Error: Migration file not found. Please ensure all files are in place."
    exit 1
fi

echo "✅ Migration file found"

# Run the migration
echo ""
echo "🚀 Running database migration..."
if command -v bundle &> /dev/null; then
    bundle exec rails db:migrate
else
    rails db:migrate
fi

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully!"
else
    echo "❌ Migration failed. Please check the error messages above."
    exit 1
fi

# Test the application
echo ""
echo "🧪 Testing the fix..."
echo "Please test your application by:"
echo "1. Starting the Rails server: rails server"
echo "2. Visiting a page with the navbar"
echo "3. Checking that user statistics display correctly"
echo "4. Verifying no database errors in the logs"

echo ""
echo "🎉 Fix completed! Your navbar should now work without database errors."
echo ""
echo "📚 For more details, see SOLID_CACHE_FIX.md"
