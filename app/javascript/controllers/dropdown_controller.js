import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["menu", "trigger"]
  static classes = ["open", "closed"]
  static values = { 
    placement: { type: String, default: "bottom-end" },
    offset: { type: Number, default: 8 },
    closeOnOutsideClick: { type: <PERSON><PERSON>an, default: true },
    closeOnEscape: { type: <PERSON>olean, default: true }
  }
  
  connect() {
    this.close()
    this.setupAccessibility()
    this.boundHandleOutsideClick = this.handleOutsideClick.bind(this)
    this.boundHandleEscape = this.handleEscape.bind(this)
  }
  
  disconnect() {
    this.removeEventListeners()
    this.close()
  }
  
  toggle(event) {
    event?.preventDefault()
    event?.stopPropagation()
    
    if (this.isOpen()) {
      this.close()
    } else {
      this.open()
    }
  }
  
  open() {
    if (this.isOpen()) return
    
    // Close other dropdowns first
    this.closeOtherDropdowns()
    
    this.menuTarget.classList.remove("hidden")
    this.menuTarget.classList.add("animate-in", "fade-in-0", "zoom-in-95")
    
    // Update ARIA attributes
    this.updateAriaAttributes(true)
    
    // Add event listeners
    this.addEventListeners()
    
    // Position dropdown
    this.positionDropdown()
    
    // Focus management
    this.manageFocus()
  }
  
  close() {
    if (!this.isOpen()) return
    
    this.menuTarget.classList.add("hidden")
    this.menuTarget.classList.remove("animate-in", "fade-in-0", "zoom-in-95")
    
    // Update ARIA attributes
    this.updateAriaAttributes(false)
    
    // Remove event listeners
    this.removeEventListeners()
    
    // Return focus to trigger
    if (this.hasTriggerTarget) {
      this.triggerTarget.focus()
    }
  }
  
  hide(event) {
    // Called by click@window->dropdown#hide
    if (event && this.element.contains(event.target)) {
      return
    }
    this.close()
  }
  
  isOpen() {
    return !this.menuTarget.classList.contains("hidden")
  }
  
  setupAccessibility() {
    // Set up ARIA attributes
    const triggerId = this.triggerTarget?.id || `dropdown-trigger-${Math.random().toString(36).substr(2, 9)}`
    const menuId = this.menuTarget.id || `dropdown-menu-${Math.random().toString(36).substr(2, 9)}`
    
    if (this.hasTriggerTarget) {
      this.triggerTarget.id = triggerId
      this.triggerTarget.setAttribute("aria-haspopup", "true")
      this.triggerTarget.setAttribute("aria-controls", menuId)
    }
    
    this.menuTarget.id = menuId
    this.menuTarget.setAttribute("role", "menu")
    this.menuTarget.setAttribute("aria-labelledby", triggerId)
    
    this.updateAriaAttributes(false)
  }
  
  updateAriaAttributes(isOpen) {
    if (this.hasTriggerTarget) {
      this.triggerTarget.setAttribute("aria-expanded", isOpen.toString())
    }
    this.menuTarget.setAttribute("aria-hidden", (!isOpen).toString())
  }
  
  addEventListeners() {
    if (this.closeOnOutsideClickValue) {
      document.addEventListener("click", this.boundHandleOutsideClick, true)
    }
    if (this.closeOnEscapeValue) {
      document.addEventListener("keydown", this.boundHandleEscape)
    }
  }
  
  removeEventListeners() {
    document.removeEventListener("click", this.boundHandleOutsideClick, true)
    document.removeEventListener("keydown", this.boundHandleEscape)
  }
  
  handleOutsideClick(event) {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }
  
  handleEscape(event) {
    if (event.key === "Escape" && this.isOpen()) {
      event.preventDefault()
      this.close()
    }
  }
  
  closeOtherDropdowns() {
    // Close other dropdown instances
    document.querySelectorAll('[data-controller*="dropdown"]').forEach(element => {
      if (element !== this.element) {
        const controller = this.application.getControllerForElementAndIdentifier(element, "dropdown")
        if (controller && controller.isOpen()) {
          controller.close()
        }
      }
    })
  }
  
  positionDropdown() {
    // Basic positioning - can be enhanced with a positioning library
    const rect = this.triggerTarget?.getBoundingClientRect()
    if (!rect) return
    
    const menu = this.menuTarget
    const placement = this.placementValue
    
    // Reset positioning
    menu.style.position = "absolute"
    menu.style.zIndex = "50"
    
    // Apply placement-based positioning
    switch (placement) {
      case "bottom-start":
        menu.style.top = `${this.offsetValue}px`
        menu.style.left = "0"
        break
      case "bottom-end":
        menu.style.top = `${this.offsetValue}px`
        menu.style.right = "0"
        break
      case "top-start":
        menu.style.bottom = `${this.offsetValue}px`
        menu.style.left = "0"
        break
      case "top-end":
        menu.style.bottom = `${this.offsetValue}px`
        menu.style.right = "0"
        break
    }
  }
  
  manageFocus() {
    // Focus first focusable element in dropdown
    const focusableElements = this.menuTarget.querySelectorAll(
      'a[href], button, [tabindex]:not([tabindex="-1"]), input, select, textarea'
    )
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }
  }
}