class AddKeyHashToSolidCacheEntries < ActiveRecord::Migration[7.1]
  def up
    # Add the key_hash column that newer versions of solid_cache require
    add_column :solid_cache_entries, :key_hash, :bigint, null: true

    # Clear existing cache entries since we can't reliably compute key_hash for existing keys
    # This is safe since cache is meant to be ephemeral
    execute "DELETE FROM solid_cache_entries"

    # Make key_hash not null after clearing
    change_column_null :solid_cache_entries, :key_hash, false

    # Add index on key_hash for performance
    add_index :solid_cache_entries, :key_hash, unique: true

    # Remove the old key index and add a new one with proper name
    remove_index :solid_cache_entries, :key if index_exists?(:solid_cache_entries, :key)
    add_index :solid_cache_entries, :key, unique: true, name: "index_solid_cache_entries_on_key"
  end

  def down
    remove_index :solid_cache_entries, :key_hash if index_exists?(:solid_cache_entries, :key_hash)
    remove_column :solid_cache_entries, :key_hash
  end
end
